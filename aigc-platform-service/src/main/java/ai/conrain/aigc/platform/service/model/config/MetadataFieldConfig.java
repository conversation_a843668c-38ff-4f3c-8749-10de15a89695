package ai.conrain.aigc.platform.service.model.config;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 元数据字段动态配置
 * 用于支持 metadata 字段的动态查询配置
 */
@Data
public class MetadataFieldConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字段名称（metadata中的key）
     */
    private String fieldName;

    /**
     * 字段显示名称
     */
    private String displayName;

    /**
     * 字段类型：STRING, BOOLEAN, NUMBER, ENUM
     */
    private FieldType fieldType;

    /**
     * 空值时的默认值
     */
    private String nullValue;

    /**
     * 是否启用多选
     */
    private Boolean multiSelect;

    /**
     * 可选值列表（当fieldType为ENUM时使用）
     */
    private List<FieldOption> options;

    /**
     * 是否必填
     */
    private Boolean required;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 字段描述
     */
    private String description;

    /**
     * 字段类型枚举
     */
    public enum FieldType {
        STRING,    // 字符串类型
        BOOLEAN,   // 布尔类型
        NUMBER,    // 数字类型
        ENUM       // 枚举类型
    }

    /**
     * 字段选项配置
     */
    @Data
    public static class FieldOption implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 选项值
         */
        private String value;

        /**
         * 选项显示名称
         */
        private String label;

        /**
         * 选项描述
         */
        private String description;

        /**
         * 是否默认选中
         */
        private Boolean defaultSelected;

        /**
         * 排序权重
         */
        private Integer sortOrder;

        public FieldOption() {}

        public FieldOption(String value, String label) {
            this.value = value;
            this.label = label;
        }

        public FieldOption(String value, String label, String description) {
            this.value = value;
            this.label = label;
            this.description = description;
        }
    }
}
